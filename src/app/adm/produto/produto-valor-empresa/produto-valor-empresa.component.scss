@import "projects/ui/assets/import.scss";

/*
   Este componente usa as classes genéricas definidas em styles-adm.scss
   As classes compact-card-padding, compact-form, form-row-centered, etc.
   estão disponíveis globalmente para manter consistência visual
*/

.section-header {
	margin: 20px 0 10px 0;
	padding: 10px 0;
	border-bottom: 1px solid #e0e0e0;

	h4 {
		margin: 0;
		color: #333;
		font-weight: 600;
		font-size: 16px;
	}
}

.table-section {
	position: relative;
	margin-top: 20px;

	.table-wrapper {
		border: 1px solid #dee2e6;
		border-radius: 4px;
		overflow: hidden;
		background: white;

		.table {
			margin-bottom: 0;
			font-size: 12px;

			th {
				background-color: #f8f9fa;
				border-bottom: 2px solid #dee2e6;
				font-weight: 600;
				color: #495057;
				padding: 8px 6px;
				text-align: center;
				vertical-align: middle;
				font-size: 12px;
			}

			td {
				padding: 6px 4px;
				vertical-align: middle;
				border-bottom: 1px solid #dee2e6;
				text-align: center;
				font-size: 12px;

				&.empresa-nome {
					text-align: left;
					font-weight: 500;
					color: #333;
					max-width: 200px;
					word-wrap: break-word;
				}

				&.valor-cell {
					text-align: right;
					font-weight: 500;
					color: #333;
				}

				&.opcoes-cell {
					text-align: center;

					i {
						margin: 0 3px;
						cursor: pointer;
						font-size: 14px;

						&:hover {
							opacity: 0.7;
						}
					}
				}
			}

			tr {
				cursor: pointer;
				transition: background-color 0.2s ease;

				&:hover {
					background-color: #f8f9fa;
				}

				&.selected {
					background-color: #e3f2fd;
					border-left: 3px solid #2196f3;
				}
			}

			.empresa-column {
				width: 25%;
			}

			.valor-column {
				width: 15%;
			}

			.opcoes-column {
				width: 10%;
			}
		}
	}
}

// Responsividade
@media (max-width: 768px) {
	.table-section {
		.table-wrapper {
			overflow-x: auto;

			.table {
				min-width: 800px;

				th,
				td {
					padding: 4px 2px;
					font-size: 11px;
				}
			}
		}
	}
}
