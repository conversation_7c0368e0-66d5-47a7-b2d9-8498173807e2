<div *ngIf="produtoFormService.isFormInitialized">
	<div class="section-header">
		<h4>Configurar valor por empresa</h4>
	</div>

	<!-- Formulário de entrada -->
	<div
		*ngIf="editandoIndex >= 0 || empresasDisponiveis.length > 0"
		class="compact-form">
		<!-- Empresa -->
		<div class="form-row-centered">
			<div class="label-column">
				<label>Empresa:</label>
			</div>
			<div class="input-column">
				<ds3-form-field>
					<ds3-select
						[options]="
							editandoIndex >= 0
								? produtoFormService.empresas
								: empresasDisponiveis
						"
						[useFullOption]="true"
						[placeholder]="'Selecione a empresa'"
						[valueKey]="'codigo'"
						[nameKey]="'nome'"
						[(ngModel)]="empresaSelecionada"
						[disabled]="editandoIndex >= 0"
						ds3Input></ds3-select>
				</ds3-form-field>
			</div>
		</div>

		<!-- Valor padrão -->
		<div class="form-row-centered">
			<div class="label-column">
				<label>Valor padrão:</label>
			</div>
			<div class="input-column">
				<ds3-form-field>
					<input
						type="text"
						ds3Input
						[(ngModel)]="valor"
						currencyMask
						[options]="{ prefix: '', thousands: '.', decimal: ',' }"
						placeholder="0,00" />
				</ds3-form-field>
			</div>
		</div>

		<!-- Valor para alunos Gogood -->
		<div class="form-row-centered">
			<div class="label-column">
				<label>Valor para alunos Gogood:</label>
			</div>
			<div class="input-column">
				<ds3-form-field>
					<input
						type="text"
						ds3Input
						[(ngModel)]="valorGogood"
						currencyMask
						[options]="{ prefix: '', thousands: '.', decimal: ',' }"
						placeholder="0,00" />
				</ds3-form-field>
			</div>
		</div>

		<!-- Valor para alunos Wellhub -->
		<div class="form-row-centered">
			<div class="label-column">
				<label>Valor para alunos Wellhub:</label>
			</div>
			<div class="input-column">
				<ds3-form-field>
					<input
						type="text"
						ds3Input
						[(ngModel)]="valorGympass"
						currencyMask
						[options]="{ prefix: '', thousands: '.', decimal: ',' }"
						placeholder="0,00" />
				</ds3-form-field>
			</div>
		</div>

		<!-- Valor para alunos Totalpass -->
		<div class="form-row-centered">
			<div class="label-column">
				<label>Valor para alunos Totalpass:</label>
			</div>
			<div class="input-column">
				<ds3-form-field>
					<input
						type="text"
						ds3Input
						[(ngModel)]="valorTotalpass"
						currencyMask
						[options]="{ prefix: '', thousands: '.', decimal: ',' }"
						placeholder="0,00" />
				</ds3-form-field>
			</div>
		</div>
	</div>

	<!-- Botões de ação -->
	<div
		*ngIf="editandoIndex >= 0 || empresasDisponiveis.length > 0"
		class="button-actions-centered">
		<button
			ds3-flat-button
			type="button"
			(click)="adicionarEmpresa()"
			[disabled]="!empresaSelecionada">
			{{ editandoIndex >= 0 ? "Atualizar" : "Adicionar" }}
		</button>
		<button
			ds3-outlined-button
			type="button"
			(click)="cancelarEdicao()"
			*ngIf="editandoIndex >= 0">
			Cancelar
		</button>
	</div>

	<!-- Tabela de resultados -->
	<div class="table-section">
		<div class="table-wrapper">
			<table class="table table-bordered">
				<thead>
					<tr>
						<th class="empresa-column">Empresa</th>
						<th class="valor-column">Valor padrão</th>
						<th class="valor-column">Valor Gogood</th>
						<th class="valor-column">Valor Wellhub</th>
						<th class="valor-column">Valor Totalpass</th>
						<th class="opcoes-column">Opções</th>
					</tr>
				</thead>
				<tbody>
					<tr
						*ngFor="let empresa of valoresEmpresa; let i = index"
						(click)="editarEmpresa(i)"
						[class.selected]="editandoIndex === i">
						<td class="empresa-nome">{{ empresa.nomeEmpresa }}</td>
						<td class="valor-cell">
							{{ empresa.valor | currency : "BRL" : "symbol" : "1.2-2" }}
						</td>
						<td class="valor-cell">
							{{ empresa.valorGogood | currency : "BRL" : "symbol" : "1.2-2" }}
						</td>
						<td class="valor-cell">
							{{ empresa.valorGympass | currency : "BRL" : "symbol" : "1.2-2" }}
						</td>
						<td class="valor-cell">
							{{
								empresa.valorTotalpass | currency : "BRL" : "symbol" : "1.2-2"
							}}
						</td>
						<td class="opcoes-cell">
							<i
								class="pct pct-check-circle text-success"
								title="Configurado"></i>
							<i
								class="pct pct-x-circle text-danger"
								title="Remover"
								(click)="removerEmpresa(i); $event.stopPropagation()"></i>
						</td>
					</tr>
					<tr *ngIf="valoresEmpresa.length === 0">
						<td colspan="6" class="text-center text-muted">
							Nenhuma empresa configurada
						</td>
					</tr>
				</tbody>
			</table>
		</div>
	</div>
</div>
