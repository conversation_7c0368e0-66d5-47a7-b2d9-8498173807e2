<div *ngIf="produtoFormService.isFormInitialized; else loading">
	<pacto-cat-card-plain
		class="compact-card-padding"
		[formGroup]="produtoFormService.form">
		<div class="compact-form">
			<!-- Código -->
			<div *ngIf="produtoFormService.isEdicao" class="form-row-centered">
				<div class="label-column">
					<label>Código:</label>
				</div>
				<div class="input-column">
					<ds3-form-field>
						<input type="text" ds3Input formControlName="codigo" readonly />
					</ds3-form-field>
				</div>
			</div>

			<!-- Importação de produtos -->
			<div class="form-row-centered">
				<div class="label-column">
					<label>Importação de produtos:</label>
				</div>
				<div class="input-column">
					<button type="button" ds3-text-button (click)="onImportarProdutos()">
						Clique aqui
					</button>
				</div>
			</div>

			<!-- Descrição -->
			<div class="form-row-centered">
				<div class="label-column">
					<label>* Descrição:</label>
				</div>
				<div class="input-column">
					<ds3-form-field>
						<input type="text" ds3Input formControlName="descricao" />
					</ds3-form-field>
				</div>
			</div>

			<!-- Tipo de Produto -->
			<div class="form-row-centered">
				<div class="label-column">
					<label>* Tipo de Produto:</label>
				</div>
				<div class="input-column">
					<ds3-form-field>
						<ds3-select
							[options]="produtoFormService.tiposProduto"
							[useFullOption]="false"
							[placeholder]="'Selecione o tipo'"
							[valueKey]="'sigla'"
							[nameKey]="'descricao'"
							formControlName="tipoProduto"
							ds3Input></ds3-select>
					</ds3-form-field>
				</div>
			</div>

			<!-- Unidade de Medida -->
			<div class="form-row-centered">
				<div class="label-column">
					<label>Unidade de Medida:</label>
				</div>
				<div class="input-column">
					<ds3-form-field>
						<ds3-select
							[options]="produtoFormService.unidadesMedida"
							[useFullOption]="false"
							[placeholder]="'Selecione a unidade'"
							[valueKey]="'codigo'"
							[nameKey]="'descricao'"
							formControlName="unidadeMedida"
							ds3Input></ds3-select>
					</ds3-form-field>
				</div>
			</div>

			<!-- Desativado -->
			<div class="form-row-centered">
				<div class="label-column">
					<label>Desativado:</label>
				</div>
				<div class="input-column">
					<ds3-checkbox formControlName="desativado"></ds3-checkbox>
				</div>
			</div>

			<!-- Categoria de Produto -->
			<div class="form-row-centered">
				<div class="label-column">
					<label>* Categoria de Produto:</label>
				</div>
				<div class="input-column">
					<ds3-form-field>
						<ds3-select
							[options]="produtoFormService.categoriasProduto"
							[useFullOption]="true"
							[placeholder]="'Selecione a categoria'"
							[valueKey]="'codigo'"
							[nameKey]="'descricao'"
							formControlName="categoriaProduto"
							ds3Input></ds3-select>
					</ds3-form-field>
				</div>
			</div>

			<!-- Valor -->
			<div class="form-row-centered">
				<div class="label-column">
					<label>* Valor:</label>
				</div>
				<div class="input-column">
					<ds3-form-field>
						<input
							ds3Input
							formControlName="valorFinal"
							currencyMask
							[options]="{ prefix: 'R$ ', thousands: '.', decimal: ',' }" />
					</ds3-form-field>
				</div>
			</div>

			<!-- Prefixo -->
			<div class="form-row-centered">
				<div class="label-column">
					<label>Prefixo:</label>
				</div>
				<div class="input-column">
					<ds3-form-field>
						<input
							type="text"
							ds3Input
							maxlength="4"
							formControlName="prefixo" />
					</ds3-form-field>
				</div>
			</div>

			<!-- Quantidade Pontos -->
			<div class="form-row-centered">
				<div class="label-column">
					<label>Quantidade Pontos:</label>
				</div>
				<div class="input-column">
					<ds3-form-field>
						<input type="number" ds3Input formControlName="quantidadePontos" />
					</ds3-form-field>
				</div>
			</div>

			<!-- Texto Contrato do Produto -->
			<div class="form-row-centered">
				<div class="label-column">
					<label>Texto Contrato do Produto:</label>
				</div>
				<div class="input-column">
					<ds3-form-field>
						<ds3-select
							[options]="produtoFormService.textosContrato"
							[useFullOption]="false"
							[placeholder]="'Selecione o texto'"
							[valueKey]="'codigo'"
							[nameKey]="'descricao'"
							formControlName="textoContratoProduto"
							ds3Input></ds3-select>
					</ds3-form-field>
				</div>
			</div>

			<!-- Observação -->
			<div class="form-row-centered">
				<div class="label-column">
					<label>Observação:</label>
				</div>
				<div class="input-column">
					<ds3-form-field>
						<textarea
							ds3Input
							rows="3"
							maxlength="255"
							formControlName="observacao"></textarea>
					</ds3-form-field>
				</div>
			</div>
		</div>
		<adm-produto-valor-empresa></adm-produto-valor-empresa>
		<adm-produto-valor-empresa-plano></adm-produto-valor-empresa-plano>
	</pacto-cat-card-plain>
</div>

<ng-template #loading>
	<div>Carregando formulário...</div>
</ng-template>
