import { Component, OnInit } from "@angular/core";
import { FormBuilder, FormGroup, Validators } from "@angular/forms";
import { ProdutoFormService } from "../produto-form.service";

@Component({
	selector: "adm-produto-comissao",
	templateUrl: "./produto-comissao.component.html",
	styleUrls: ["./produto-comissao.component.scss"],
})
export class ProdutoComissaoComponent implements OnInit {
	comissoes: any[] = [];
	comissaoForm: FormGroup;
	editandoIndex: number = -1;

	constructor(
		public produtoFormService: ProdutoFormService,
		private fb: FormBuilder
	) {
		this.criarFormularioComissao();
	}

	ngOnInit(): void {
		this.carregarComissoes();
		this.produtoFormService.comissoesProduto$.subscribe((comissoes) => {
			this.comissoes = comissoes;
		});
	}

	private criarFormularioComissao(): void {
		this.comissaoForm = this.fb.group({
			empresa: [null, Validators.required],
			vigenciaInicio: ["", Validators.required],
			vigenciaFinal: ["", Validators.required],
			porcentagem: [0, [Validators.min(0), Validators.max(100)]],
			valorFixo: [0, Validators.min(0)],
		});
	}

	carregarComissoes() {
		this.comissoes = this.produtoFormService.comissoesProduto;
	}

	onAdicionarComissao() {
		if (this.comissaoForm.valid) {
			const novaComissao = this.criarComissaoFromForm();

			if (this.editandoIndex >= 0) {
				this.produtoFormService.editarComissao(this.editandoIndex, novaComissao);
				this.editandoIndex = -1;
			} else {
				this.produtoFormService.adicionarComissao(novaComissao);
			}

			this.comissaoForm.reset();
			this.resetarFormulario();
		}
	}

	onEditarComissao(comissao: any, index: number) {
		this.editandoIndex = index;
		this.comissaoForm.patchValue({
			empresa: comissao.empresa.codigo || comissao.empresa,
			vigenciaInicio: this.formatarDataParaInput(comissao.vigenciaInicio),
			vigenciaFinal: this.formatarDataParaInput(comissao.vigenciaFinal),
			porcentagem: comissao.porcentagem,
			valorFixo: comissao.valorFixo,
		});
	}

	onExcluirComissao(index: number) {
		if (confirm("Tem certeza que deseja excluir esta comissão?")) {
			this.produtoFormService.excluirComissao(index);
		}
	}

	onConfigurarComissao() {
		const formData = this.produtoFormService.form.value;
		if (formData.empresaComercial && formData.vigenciaInicialComercial &&
			formData.vigenciaFinalComercial && (formData.porcentagemComercial > 0 || formData.valorFixoComercial > 0)) {

			const novaComissao = {
				codigo: null,
				produto: {
					codigo: this.produtoFormService.produto.codigo,
					descricao: this.produtoFormService.produto.descricao,
				},
				valorFixo: formData.valorFixoComercial,
				porcentagem: formData.porcentagemComercial,
				empresa: this.produtoFormService.empresas.find(emp => emp.codigo === formData.empresaComercial),
				vigenciaInicio: formData.vigenciaInicialComercial,
				vigenciaFinal: formData.vigenciaFinalComercial,
				itemApresentar: `PRODUTO: ${this.produtoFormService.produto.descricao || ''}`
			};

			this.produtoFormService.adicionarComissao(novaComissao);

			this.produtoFormService.form.patchValue({
				empresaComercial: null,
				vigenciaInicialComercial: null,
				vigenciaFinalComercial: null,
				porcentagemComercial: 0,
				valorFixoComercial: 0
			});
		}
	}

	private criarComissaoFromForm(): any {
		const formValue = this.comissaoForm.value;
		const empresaSelecionada = this.produtoFormService.empresas.find(emp => emp.codigo === formValue.empresa);

		return {
			codigo: null,
			produto: {
				codigo: this.produtoFormService.produto.codigo,
				descricao: this.produtoFormService.produto.descricao,
			},
			valorFixo: formValue.valorFixo,
			porcentagem: formValue.porcentagem,
			empresa: empresaSelecionada,
			vigenciaInicio: formValue.vigenciaInicio,
			vigenciaFinal: formValue.vigenciaFinal,
			itemApresentar: `PRODUTO: ${this.produtoFormService.produto.descricao || ''}`
		};
	}

	private resetarFormulario(): void {
		this.comissaoForm.patchValue({
			empresa: null,
			vigenciaInicio: "",
			vigenciaFinal: "",
			porcentagem: 0,
			valorFixo: 0,
		});
	}

	private formatarDataParaInput(data: string): string {
		if (!data) { return ""; }
		const date = new Date(data);
		return date.toISOString().split('T')[0];
	}

	formatarDataParaExibicao(data: string): string {
		if (!data) { return ""; }
		const date = new Date(data);
		return date.toLocaleDateString('pt-BR');
	}

	get isEditando(): boolean {
		return this.editandoIndex >= 0;
	}

	cancelarEdicao(): void {
		this.editandoIndex = -1;
		this.comissaoForm.reset();
		this.resetarFormulario();
	}
}
