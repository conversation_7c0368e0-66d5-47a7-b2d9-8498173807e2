@import "projects/ui/assets/import.scss";

/*
   Este componente usa as classes genéricas definidas em styles-adm.scss
   As classes compact-card-padding, compact-form, form-row-centered, etc.
   estão disponíveis globalmente para manter consistência visual
*/

.table-section {
	margin-top: 30px;

	.table-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 15px;

		h4 {
			margin: 0;
			color: #333;
			font-weight: 600;
		}
	}

	.table-container {
		background: white;
		border-radius: 4px;
		box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
		overflow: hidden;

		.table {
			margin: 0;

			thead {
				background-color: #f8f9fa;

				th {
					border-top: none;
					border-bottom: 2px solid #dee2e6;
					font-weight: 600;
					color: #495057;
					padding: 12px 8px;
					font-size: 0.875rem;
				}
			}

			tbody {
				tr {
					&:hover {
						background-color: #f8f9fa;
					}
				}

				td {
					padding: 12px 8px;
					vertical-align: middle;
					border-top: 1px solid #dee2e6;
					font-size: 0.875rem;
				}
			}
		}
	}

	.action-buttons {
		display: flex;
		gap: 5px;

		button {
			padding: 4px 8px;
			min-width: auto;

			&:hover {
				background-color: rgba(0, 0, 0, 0.1);
			}
		}
	}

	.no-data-message {
		text-align: center;
		padding: 40px 20px;
		color: #6c757d;
		font-style: italic;

		p {
			margin: 0;
		}
	}
}
