<div *ngIf="produtoFormService.isFormInitialized; else loading">
	<pacto-cat-card-plain
		class="compact-card-padding"
		[formGroup]="produtoFormService.form">
		<div class="compact-form">
			<!-- Empresa -->
			<div class="form-row-centered">
				<div class="label-column">
					<label>Empresa:</label>
				</div>
				<div class="input-column">
					<ds3-form-field>
						<ds3-select
							[options]="produtoFormService.empresas"
							[useFullOption]="false"
							[placeholder]="'Selecione a empresa'"
							[valueKey]="'codigo'"
							[nameKey]="'nome'"
							formControlName="empresaComercial"
							ds3Input></ds3-select>
					</ds3-form-field>
				</div>
			</div>

			<!-- Vigência Inicial -->
			<div class="form-row-centered">
				<div class="label-column">
					<label>Vigência Inicial:</label>
				</div>
				<div class="input-column">
					<ds3-form-field>
						<input
							type="date"
							ds3Input
							formControlName="vigenciaInicialComercial" />
					</ds3-form-field>
				</div>
			</div>

			<!-- Vigência Final -->
			<div class="form-row-centered">
				<div class="label-column">
					<label>Vigência Final:</label>
				</div>
				<div class="input-column">
					<ds3-form-field>
						<input
							type="date"
							ds3Input
							formControlName="vigenciaFinalComercial" />
					</ds3-form-field>
				</div>
			</div>

			<!-- Porcentagem -->
			<div class="form-row-centered">
				<div class="label-column">
					<label>Porcentagem:</label>
				</div>
				<div class="input-column">
					<ds3-form-field>
						<input
							type="number"
							step="0.01"
							ds3Input
							formControlName="porcentagemComercial" />
					</ds3-form-field>
				</div>
			</div>

			<!-- Valor Fixo -->
			<div class="form-row-centered">
				<div class="label-column">
					<label>Valor Fixo:</label>
				</div>
				<div class="input-column">
					<ds3-form-field>
						<input
							type="number"
							step="0.01"
							ds3Input
							formControlName="valorFixoComercial" />
					</ds3-form-field>
				</div>
			</div>

			<!-- Botão Configurar -->
			<div class="form-row-centered">
				<div class="label-column"></div>
				<div class="input-column">
					<button
						type="button"
						ds3-flat-button
						(click)="onConfigurarComissao()"></button>
				</div>
			</div>

			<!-- Tabela de Comissões -->
			<div class="table-section">
				<div class="table-header">
					<h4>Comissões Configuradas</h4>
					<button type="button" ds3-flat-button (click)="onAdicionarComissao()">
						Adicionar
					</button>
				</div>

				<div class="table-container">
					<table class="table table-striped">
						<thead>
							<tr>
								<th>Código</th>
								<th>Vl Taxa (R$)</th>
								<th>Porc (%)</th>
								<th>Vig Início</th>
								<th>Vig Final</th>
								<th>Empresa</th>
								<th>Opções</th>
							</tr>
						</thead>
						<tbody>
							<tr *ngFor="let comissao of comissoes">
								<td>{{ comissao.codigo }}</td>
								<td>
									{{ comissao.vlTaxa | currency : "BRL" : "symbol" : "1.2-2" }}
								</td>
								<td>{{ comissao.porc | number : "1.2-2" }}%</td>
								<td>{{ comissao.vigInicio }}</td>
								<td>{{ comissao.vigFinal }}</td>
								<td>{{ comissao.empresa }}</td>
								<td>
									<div class="action-buttons">
										<button
											type="button"
											ds3-icon-button
											(click)="onEditarComissao(comissao)"
											title="Editar"></button>
										<button
											type="button"
											ds3-icon-button
											(click)="onExcluirComissao(comissao)"
											title="Excluir"></button>
									</div>
								</td>
							</tr>
						</tbody>
					</table>
				</div>

				<!-- Mensagem quando não há dados -->
				<div *ngIf="comissoes.length === 0" class="no-data-message">
					<p>Nenhuma comissão configurada.</p>
				</div>
			</div>
		</div>
	</pacto-cat-card-plain>
</div>

<ng-template #loading>
	<div>Carregando formulário...</div>
</ng-template>
