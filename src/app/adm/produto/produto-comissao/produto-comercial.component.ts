import { Component, OnInit } from "@angular/core";
import { ProdutoFormService } from "../produto-form.service";

@Component({
	selector: "adm-produto-comercial",
	templateUrl: "./produto-comercial.component.html",
	styleUrls: ["./produto-comercial.component.scss"],
})
export class ProdutoComercialComponent implements OnInit {
	// Dados da tabela de comissões
	comissoes: any[] = [];

	constructor(public produtoFormService: ProdutoFormService) {}

	ngOnInit(): void {
		this.carregarComissoes();
	}

	carregarComissoes() {
		this.comissoes = [];
	}

	onAdicionarComissao() {
		// Implementar lógica para adicionar nova comissão
		console.log("Adicionar comissão clicked");
	}

	onEditarComissao(comissao: any) {
		// Implementar lógica para editar comissão
		console.log("Editar comissão:", comissao);
	}

	onExcluirComissao(comissao: any) {
		// Implementar lógica para excluir comissão
		console.log("Excluir comissão:", comissao);
	}

	onConfigurarComissao() {
		// Implementar lógica de configuração de comissão
		console.log("Configurar comissão clicked");
	}
}
