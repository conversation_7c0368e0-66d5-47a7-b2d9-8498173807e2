<div *ngIf="produtoFormService.isFormInitialized">
	<div class="section-header">
		<h4>Configurar valor por empresa e plano</h4>
	</div>

	<!-- Formulário de entrada -->
	<div
		*ngIf="editandoIndex >= 0 || empresasDisponiveis.length > 0"
		class="compact-form">
		<!-- Empresa -->
		<div class="form-row-centered">
			<div class="label-column">
				<label>Empresa:</label>
			</div>
			<div class="input-column">
				<ds3-form-field>
					<ds3-select
						[options]="
							editandoIndex >= 0
								? produtoFormService.empresas
								: empresasDisponiveis
						"
						[useFullOption]="true"
						[placeholder]="'Selecione a empresa'"
						[valueKey]="'codigo'"
						[nameKey]="'nome'"
						[(ngModel)]="empresaSelecionada"
						[disabled]="editandoIndex >= 0"
						(ngModelChange)="onEmpresaChange()"
						ds3Input></ds3-select>
				</ds3-form-field>
			</div>
		</div>

		<!-- Plano -->
		<div class="form-row-centered">
			<div class="label-column">
				<label>Plano:</label>
			</div>
			<div class="input-column">
				<ds3-form-field>
					<ds3-select
						[options]="planosDisponiveis"
						[useFullOption]="true"
						[placeholder]="'Selecione o plano'"
						[valueKey]="'codigo'"
						[nameKey]="'descricao'"
						[(ngModel)]="planoSelecionado"
						[disabled]="editandoIndex >= 0"
						ds3Input></ds3-select>
				</ds3-form-field>
			</div>
		</div>

		<!-- Valor -->
		<div class="form-row-centered">
			<div class="label-column">
				<label>Valor:</label>
			</div>
			<div class="input-column">
				<ds3-form-field>
					<input
						type="text"
						ds3Input
						[(ngModel)]="valor"
						currencyMask
						[options]="{ prefix: '', thousands: '.', decimal: ',' }"
						placeholder="0,00" />
				</ds3-form-field>
			</div>
		</div>
	</div>

	<!-- Botões de ação -->
	<div
		*ngIf="editandoIndex >= 0 || empresasDisponiveis.length > 0"
		class="button-actions-centered">
		<button
			ds3-flat-button
			type="button"
			(click)="adicionarEmpresaPlano()"
			[disabled]="!empresaSelecionada || !planoSelecionado">
			{{ editandoIndex >= 0 ? "Atualizar" : "Adicionar" }}
		</button>
		<button
			ds3-outlined-button
			type="button"
			(click)="cancelarEdicao()"
			*ngIf="editandoIndex >= 0">
			Cancelar
		</button>
	</div>

	<!-- Tabela de resultados -->
	<div class="table-section">
		<div class="table-wrapper">
			<table class="table table-bordered">
				<thead>
					<tr>
						<th class="empresa-column">Empresa</th>
						<th class="plano-column">Plano</th>
						<th class="valor-column">Valor</th>
						<th class="opcoes-column">Opções</th>
					</tr>
				</thead>
				<tbody>
					<tr
						*ngFor="let item of valoresEmpresaPlano; let i = index"
						(click)="editarEmpresaPlano(i)"
						[class.selected]="editandoIndex === i">
						<td class="empresa-nome">{{ item.nomeEmpresa }}</td>
						<td class="plano-nome">{{ item.nomePlano }}</td>
						<td class="valor-cell">
							{{ item.valor | currency : "BRL" : "symbol" : "1.2-2" }}
						</td>
						<td class="opcoes-cell">
							<i
								class="pct pct-check-circle text-success"
								title="Configurado"></i>
							<i
								class="pct pct-x-circle text-danger"
								title="Remover"
								(click)="removerEmpresaPlano(i); $event.stopPropagation()"></i>
						</td>
					</tr>
					<tr *ngIf="valoresEmpresaPlano.length === 0">
						<td colspan="4" class="text-center text-muted">
							Nenhuma configuração de empresa e plano encontrada
						</td>
					</tr>
				</tbody>
			</table>
		</div>
	</div>
</div>
