import { ChangeDetectorRef, Component, OnInit } from "@angular/core";
import { SnotifyService } from "ng-snotify";
import { ProdutoFormService } from "../produto-form.service";
import { ZwBootProdutoService } from "adm-legado-api";

interface ProdutoValorEmpresaPlano {
	codigo?: number;
	codigoEmpresa: number;
	codigoPlano: number;
	valor: number;
	nomeEmpresa: string;
	nomePlano: string;
}

@Component({
	selector: "adm-produto-valor-empresa-plano",
	templateUrl: "./produto-valor-empresa-plano.component.html",
	styleUrls: ["./produto-valor-empresa-plano.component.scss"],
})
export class ProdutoValorEmpresaPlanoComponent implements OnInit {
	valoresEmpresaPlano: ProdutoValorEmpresaPlano[] = [];
	empresaSelecionada: any = null;
	planoSelecionado: any = null;
	valor: number = 0;

	editandoIndex: number = -1;

	planosDisponiveis: any[] = [];

	constructor(
		public produtoFormService: ProdutoFormService,
		private cd: ChangeDetectorRef,
		private notificationService: SnotifyService,
		private produtoService: ZwBootProdutoService
	) {}

	ngOnInit(): void {
		this.carregarValoresEmpresaPlano();
	}

	private carregarValoresEmpresaPlano(): void {
		this.carregarValoresDoFormulario();
	}

	onEmpresaChange(): void {
		if (this.empresaSelecionada && this.empresaSelecionada.codigo) {
			this.planoSelecionado = null;
			this.carregarPlanosPorEmpresa(this.empresaSelecionada.codigo).catch(
				() => {
					// Erro já tratado no método carregarPlanosPorEmpresa
				}
			);
		} else {
			this.planosDisponiveis = [];
			this.planoSelecionado = null;
		}
	}

	private carregarPlanosPorEmpresa(empresaId: number): Promise<void> {
		return new Promise((resolve, reject) => {
			this.produtoService.planos(empresaId.toString()).subscribe({
				next: (response) => {
					this.planosDisponiveis = response.content || [];
					this.cd.detectChanges();
					resolve();
				},
				error: (error) => {
					console.error("Erro ao carregar planos:", error);
					this.planosDisponiveis = [];
					this.notificationService.error(
						"Erro ao carregar planos da empresa selecionada."
					);
					reject(error);
				},
			});
		});
	}

	adicionarEmpresaPlano(): void {
		if (!this.empresaSelecionada) {
			this.notificationService.warning("Selecione uma empresa.");
			return;
		}

		if (!this.planoSelecionado) {
			this.notificationService.warning("Selecione um plano.");
			return;
		}

		const novoValor: ProdutoValorEmpresaPlano = {
			codigo:
				this.editandoIndex >= 0
					? this.valoresEmpresaPlano[this.editandoIndex].codigo
					: 0,
			codigoEmpresa: this.empresaSelecionada.codigo,
			codigoPlano: this.planoSelecionado.codigo,
			nomeEmpresa: this.empresaSelecionada.nome,
			nomePlano: this.planoSelecionado.descricao,
			valor: this.valor || 0,
		};

		if (this.editandoIndex >= 0) {
			this.valoresEmpresaPlano[this.editandoIndex] = novoValor;
			this.notificationService.success("Configuração atualizada com sucesso!");
		} else {
			const configuracaoExistente = this.valoresEmpresaPlano.find(
				(v) =>
					v.codigoEmpresa === this.empresaSelecionada.codigo &&
					v.codigoPlano === this.planoSelecionado.codigo
			);
			if (configuracaoExistente) {
				this.notificationService.warning(
					"Esta combinação de empresa e plano já foi configurada."
				);
				return;
			}
			this.valoresEmpresaPlano.push(novoValor);
			this.notificationService.success("Configuração adicionada com sucesso!");
		}
		this.limparFormulario();
		this.salvarValoresNoFormulario();
	}

	editarEmpresaPlano(index: number): void {
		const item = this.valoresEmpresaPlano[index];

		this.empresaSelecionada = this.produtoFormService.empresas.find(
			(e) => e.codigo === item.codigoEmpresa
		);
		this.valor = item.valor;
		this.editandoIndex = index;

		if (this.empresaSelecionada) {
			this.carregarPlanosPorEmpresa(this.empresaSelecionada.codigo).then(() => {
				this.planoSelecionado = this.planosDisponiveis.find(
					(p) => p.codigo === item.codigoPlano
				);
			});
		}
	}

	removerEmpresaPlano(index: number): void {
		this.valoresEmpresaPlano.splice(index, 1);
		this.salvarValoresNoFormulario();
		this.notificationService.success("Configuração removida com sucesso!");

		if (this.editandoIndex === index) {
			this.cancelarEdicao();
		}
	}

	cancelarEdicao(): void {
		this.limparFormulario();
	}

	private limparFormulario(): void {
		this.empresaSelecionada = null;
		this.planoSelecionado = null;
		this.valor = 0;
		this.editandoIndex = -1;
	}

	private salvarValoresNoFormulario(): void {
		this.produtoFormService.setValoresEmpresaPlano(this.valoresEmpresaPlano);
	}

	private carregarValoresDoFormulario(): void {
		this.produtoFormService.getValoresEmpresaPlano().subscribe((value) => {
			const valoresFormulario = value;
			if (valoresFormulario && valoresFormulario.length > 0) {
				this.valoresEmpresaPlano = [...valoresFormulario];
			}
			this.cd.detectChanges();
		});
	}

	public getValoresEmpresaPlano(): ProdutoValorEmpresaPlano[] {
		return this.valoresEmpresaPlano;
	}

	get empresasDisponiveis(): any[] {
		if (!this.produtoFormService.empresas) {
			return [];
		}

		if (!this.planoSelecionado) {
			return this.produtoFormService.empresas;
		}

		return this.produtoFormService.empresas.filter(
			(empresa) =>
				!this.valoresEmpresaPlano.some(
					(valor) =>
						valor.codigoEmpresa === empresa.codigo &&
						valor.codigoPlano === this.planoSelecionado.codigo
				)
		);
	}

	get temEmpresasDisponiveis(): boolean {
		return this.empresasDisponiveis.length > 0;
	}
}
