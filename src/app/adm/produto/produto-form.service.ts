import { Injectable } from "@angular/core";
import { FormBuilder, FormGroup, Validators } from "@angular/forms";
import { BehaviorSubject, Observable } from "rxjs";
import { ZwBootProdutoService } from "adm-legado-api";
import { SessionService } from "@base-core/client/session.service";

@Injectable()
export class ProdutoFormService {
	private _form: FormGroup;
	private _produto: any = {};
	private _isEdicao = false;
	private _id: string;

	// BehaviorSubjects para dados auxiliares
	private _empresas$ = new BehaviorSubject<any[]>([]);
	private _valoresEmpresa$ = new BehaviorSubject<any[]>([]);
	private _valoresEmpresaPlano$ = new BehaviorSubject<any[]>([]);
	private _convenios$ = new BehaviorSubject<any[]>([]);
	private _categoriasProduto$ = new BehaviorSubject<any[]>([]);
	private _tiposProduto$ = new BehaviorSubject<any[]>([]);
	private _unidadesMedida$ = new BehaviorSubject<any[]>([]);
	private _tiposVigencia$ = new BehaviorSubject<any[]>([]);
	private _planos$ = new BehaviorSubject<any[]>([]);
	private _tamanhoArmarios$ = new BehaviorSubject<any[]>([]);
	private _textosContrato$ = new BehaviorSubject<any[]>([]);

	// Observables públicos
	public empresas$ = this._empresas$.asObservable();
	public convenios$ = this._convenios$.asObservable();
	public categoriasProduto$ = this._categoriasProduto$.asObservable();
	public tiposProduto$ = this._tiposProduto$.asObservable();
	public unidadesMedida$ = this._unidadesMedida$.asObservable();
	public tiposVigencia$ = this._tiposVigencia$.asObservable();
	public planos$ = this._planos$.asObservable();
	public tamanhoArmarios$ = this._tamanhoArmarios$.asObservable();
	public textosContrato$ = this._textosContrato$.asObservable();

	constructor(
		private fb: FormBuilder,
		private produtoService: ZwBootProdutoService,
		private sessionService: SessionService
	) {
		// Não inicializar o form no construtor para evitar problemas de timing
	}

	private initializeForm(): void {
		this._form = this.fb.group({
			codigo: null,
			descricao: ["", Validators.required],
			tipoProduto: [null, Validators.required],
			capacidade: [null],
			unidadeMedida: [null],
			codigoBarras: [""],
			desativado: [false],
			aparecerAulaCheia: [false],
			categoriaProduto: [null, Validators.required],
			tamanhoArmario: [null],
			precoCusto: [0],
			margemLucro: [0],
			valorFinal: [0, Validators.required],
			tipoVigencia: [null],
			vigenciaInicial: [null],
			vigenciaFinal: [null],
			nrDiasVigencia: [0],
			renovavelAutomaticamente: [false],
			prefixo: [""],
			bloqueiaPelaVigencia: [false],
			prevalecerVigenciaContrato: [false],
			quantidadePontos: [0],
			textoContratoProduto: [null],
			// Campos SESI
			codigoProdutoSesi: [""],
			cRSesi: [""],
			projetoSesi: [""],
			contaFinanceiraSesi: [""],
			negocioSesi: [""],
			observacao: [""],
			qtdConvites: [0],
			empresa: [null],
			valorPadrao: [0],
			valorAlunosCoopod: [0],
			valorAlunosGympass: [0],
			valorAlunosTotalpass: [0],
			empresaPlano: [null],
			plano: [null],
			valorPlano: [0],
			// Campos da aba Comercial
			empresaComercial: [null],
			vigenciaInicialComercial: [null],
			vigenciaFinalComercial: [null],
			porcentagemComercial: [0],
			valorFixoComercial: [0],
			// Campos da aba Fiscal
			configuracaoEmissaoNfse: [null],
			configuracaoEmissaoNfce: [null],
			cfop: [""],
			ncm: [""],
			ncmNfce: [""],
			cest: [""],
			codigoListaServico: [""],
			codigoTributacaoMunicipal: [""],
			descricaoServicoMunicipal: [""],
			enviarPercentualImpostos: [false],
			// ICMS
			situacaoTributariaIcms: [""],
			isentoIcms: [false],
			enviarAliquotaNfeIcms: [false],
			aliquotaIcms: [0],
			// CBNEF
			codigoBeneficioFiscal: [""],
			// ISSQN
			situacaoTributariaIssqn: [""],
			aliquotaIssqn: [0],
			// PIS
			situacaoTributariaPis: [""],
			isencaoPis: [false],
			enviarAliquotaPisJason: [false],
			aliquotaPis: [0],
			// COFINS
			situacaoTributariaCofins: [""],
			isencaoCofins: [false],
			enviarAliquotaCofinsJason: [false],
			aliquotaCofins: [0],
			// Campos da aba Vendas Online
			apresentarNoPactoApp: [false],
			apresentarNoVendasOnline: [false],
			apresentarNoPactoPlayer: [false],
			// Campos da aba Mesclagem
			produtoMesclagem: [null],
			// Campos existentes mantidos para outras abas
			situacao: ["ATIVO"],
			convenio: [null],
			valorAlunoDiarista: [0],
			valorAlunoEmpresa: [0],
			valorAlunoFinal: [0],
			percentualMunicipal: [0],
			percentualEstadual: [0],
			percentualFederal: [0],
			limitarAliquotaPisAoJuridico: [false],
			entrarAliquotaCofinsAoJuridico: [false],
			acrescentarNoFechaPacote: [false],
			apresentarNoVendaOnline: [false],
			quantidadeMaximaParcelas: [0],
			mensagem: [""],
		});
	}

	// Getters
	get form(): FormGroup {
		if (!this._form) {
			this.initializeForm();
		}
		return this._form;
	}

	get isFormInitialized(): boolean {
		return !!this._form;
	}

	get produto(): any {
		return this._produto;
	}

	get isEdicao(): boolean {
		return this._isEdicao;
	}

	get id(): string {
		return this._id;
	}

	get empresas(): any[] {
		return this._empresas$.value;
	}

	get convenios(): any[] {
		return this._convenios$.value;
	}

	get categoriasProduto(): any[] {
		return this._categoriasProduto$.value;
	}

	get tiposProduto(): any[] {
		return this._tiposProduto$.value;
	}

	get unidadesMedida(): any[] {
		return this._unidadesMedida$.value;
	}

	get tiposVigencia(): any[] {
		return this._tiposVigencia$.value;
	}

	get planos(): any[] {
		return this._planos$.value;
	}

	get tamanhoArmarios(): any[] {
		return this._tamanhoArmarios$.value;
	}

	get textosContrato(): any[] {
		return this._textosContrato$.value;
	}

	// Métodos de inicialização
	public inicializar(id?: string): void {
		// Garantir que o FormGroup está inicializado
		if (!this._form) {
			this.initializeForm();
		}

		this._id = id;
		this._isEdicao = !!id && id !== "novo";
		this.carregarDadosIniciais();
		if (this._isEdicao) {
			this.carregarProduto();
		} else {
			this.resetForm();
		}
	}

	public carregarDadosIniciais(): void {
		this.carregarEmpresas();
		this.carregarConvenios();
		this.carregarCategoriasProduto();
		this.carregarTiposProduto();
		this.carregarUnidadesMedida();
		this.carregarTiposVigencia();
		this.carregarPlanos();
		this.carregarTamanhoArmarios();
		this.carregarTextosContrato();
	}

	private carregarEmpresas(): void {
		this._empresas$.next(this.sessionService.empresas);
	}

	private carregarConvenios(): void {
		// Implementar quando necessário
		// this.produtoService.convenios().subscribe(
		// 	(response) => {
		// 		this._convenios$.next(response.content || []);
		// 	}
		// );
	}

	private carregarCategoriasProduto(): void {
		this.produtoService.categoriasProduto().subscribe((response) => {
			this._categoriasProduto$.next(response.content || []);
		});
	}

	private carregarTiposProduto(): void {
		this.produtoService.tiposProduto().subscribe((response) => {
			this._tiposProduto$.next(response.content || []);
		});
	}

	private carregarUnidadesMedida(): void {
		// Mock data - implementar chamada real para o serviço
		const unidadesMedida = [
			{ codigo: "UN", descricao: "Unidade (UN)" },
			{ codigo: "KG", descricao: "Quilograma (KG)" },
			{ codigo: "LT", descricao: "Litro (LT)" },
			{ codigo: "MT", descricao: "Metro (MT)" },
		];
		this._unidadesMedida$.next(unidadesMedida);
	}

	private carregarTiposVigencia(): void {
		// Mock data - implementar chamada real para o serviço
		const tiposVigencia = [
			{ codigo: "PERIODO_FIXO", descricao: "Período Fixo" },
			{ codigo: "PERIODO_VARIAVEL", descricao: "Período Variável" },
			{ codigo: "INDETERMINADO", descricao: "Indeterminado" },
		];
		this._tiposVigencia$.next(tiposVigencia);
	}

	private carregarPlanos(): void {
		// Mock data - implementar chamada real para o serviço
		const planos = [
			{ codigo: "PLANO_BASICO", descricao: "Plano Básico" },
			{ codigo: "PLANO_PREMIUM", descricao: "Plano Premium" },
			{ codigo: "PLANO_VIP", descricao: "Plano VIP" },
		];
		this._planos$.next(planos);
	}

	private carregarTamanhoArmarios(): void {
		// Mock data - implementar chamada real para o serviço
		const tamanhoArmarios = [
			{ codigo: "P", descricao: "Pequeno" },
			{ codigo: "M", descricao: "Médio" },
			{ codigo: "G", descricao: "Grande" },
		];
		this._tamanhoArmarios$.next(tamanhoArmarios);
	}

	private carregarTextosContrato(): void {
		// Mock data - implementar chamada real para o serviço
		const textosContrato = [
			{ codigo: "CONTRATO_PADRAO", descricao: "Contrato Padrão" },
			{ codigo: "CONTRATO_ESPECIAL", descricao: "Contrato Especial" },
			{ codigo: "CONTRATO_PROMOCIONAL", descricao: "Contrato Promocional" },
		];
		this._textosContrato$.next(textosContrato);
	}

	private carregarProduto(): void {
		this.produtoService.find(this._id).subscribe((response) => {
			this._produto = response.content;
			this._form.patchValue(this._produto);
			if (this._produto.valoresEmpresa) {
				this.setValoresEmpresa(this._produto.valoresEmpresa);
			}
			if (this._produto.valoresEmpresaPlano) {
				this.setValoresEmpresaPlano(this._produto.valoresEmpresaPlano);
			}
		});
	}

	public resetForm(): void {
		this._form.reset();
		this._produto = {};
		this._form.patchValue({
			codigo: null,
			desativado: false,
			aparecerAulaCheia: false,
			precoCusto: 0,
			margemLucro: 0,
			valor: 0,
			nrDiasVigencia: 0,
			renovavelAutomaticamente: false,
			prefixo: "",
			bloqueiaPelaVigencia: false,
			prevalecerVigenciaContrato: false,
			quantidadePontos: 0,
			qtdConvites: 0,
			valorPadrao: 0,
			valorAlunosCoopod: 0,
			valorAlunosGympass: 0,
			valorAlunosTotalpass: 0,
			valorPlano: 0,
			porcentagemComercial: 0,
			valorFixoComercial: 0,
			enviarPercentualImpostos: false,
			isentoIcms: false,
			enviarAliquotaNfeIcms: false,
			aliquotaIcms: 0,
			aliquotaIssqn: 0,
			isencaoPis: false,
			enviarAliquotaPisJason: false,
			aliquotaPis: 0,
			isencaoCofins: false,
			enviarAliquotaCofinsJason: false,
			aliquotaCofins: 0,
			apresentarNoPactoApp: false,
			apresentarNoVendasOnline: false,
			apresentarNoPactoPlayer: false,
			situacao: "ATIVO",
			valorAlunoDiarista: 0,
			valorAlunoEmpresa: 0,
			valorAlunoFinal: 0,
			percentualMunicipal: 0,
			percentualEstadual: 0,
			percentualFederal: 0,
			limitarAliquotaPisAoJuridico: false,
			entrarAliquotaCofinsAoJuridico: false,
			acrescentarNoFechaPacote: false,
			apresentarNoVendaOnline: false,
			quantidadeMaximaParcelas: 0,
			mensagem: "",
		});
	}

	public salvar(): Observable<any> {
		const dadosFormulario = this._form.getRawValue();
		Object.assign(this._produto, dadosFormulario);
		this._produto.valoresEmpresa = this._valoresEmpresa$.value;
		this._produto.valoresEmpresaPlano = this._valoresEmpresaPlano$.value;
		return this.produtoService.save(this._produto);
	}

	public excluir(): Observable<any> {
		return this.produtoService.delete(Number(this._id));
	}

	public isFormValid(): boolean {
		return this._form.valid;
	}

	public markFormGroupTouched(): void {
		Object.keys(this._form.controls).forEach((key) => {
			const control = this._form.get(key);
			control.markAsTouched();
		});
	}

	// Métodos para gerenciar valores por empresa
	public setValoresEmpresa(valores: any[]): void {
		if (valores && valores.length > 0) {
			valores.forEach((valorEmpresa) => {
				if (valorEmpresa.codigoEmpresa && !valorEmpresa.nomeEmpresa) {
					const empresa = this.empresas.find(
						(emp) => emp.codigo === valorEmpresa.codigoEmpresa
					);
					if (empresa) {
						valorEmpresa.nomeEmpresa = empresa.nome;
						valorEmpresa.valorGogood = 0.0;
						valorEmpresa.valorGympass = 0.0;
						valorEmpresa.valorTotalpass = 0.0;
					}
				}
			});
		}
		this._valoresEmpresa$.next(valores);
	}

	public getValoresEmpresa(): Observable<any> {
		return this._valoresEmpresa$.asObservable();
	}

	// Métodos para gerenciar valores por empresa e plano
	public setValoresEmpresaPlano(valores: any[]): void {
		if (valores && valores.length > 0) {
			valores.forEach((valorEmpresaPlano) => {
				if (valorEmpresaPlano.codigoEmpresa && !valorEmpresaPlano.nomeEmpresa) {
					const empresa = this.empresas.find(
						(emp) => emp.codigo === valorEmpresaPlano.codigoEmpresa
					);
					if (empresa) {
						valorEmpresaPlano.nomeEmpresa = empresa.nome;
					}
				}
			});
		}
		this._valoresEmpresaPlano$.next(valores);
	}

	public getValoresEmpresaPlano(): Observable<any> {
		return this._valoresEmpresaPlano$.asObservable();
	}
}
