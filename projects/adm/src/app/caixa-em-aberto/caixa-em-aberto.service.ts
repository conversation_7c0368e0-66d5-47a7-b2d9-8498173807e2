import { Injectable } from "@angular/core";
import { AdmRestService } from "../adm-rest.service";
import { HttpClient } from "@angular/common/http";
import { Observable } from "rxjs";
import { map } from "rxjs/operators";
import { ParcelasModel } from "./renegociar-parcelas/model/renegociar-model";

@Injectable({
	providedIn: "root",
})
export class CaixaEmAbertoService {
	public parcelas: any[];
	public grupos: any[];
	empresaSelecionada: number;
	pessoa: any;

	constructor(private admRest: AdmRestService, private http: HttpClient) {}

	consultar(filters: any, page: string, size: string): Observable<any> {
		return this.http
			.get(this.admRest.buildFullUrlZwBack("caixa-aberto/consultar"), {
				params: {
					filters,
					page,
					size,
				},
			})
			.pipe(
				map((response: any) => {
					return response.content;
				})
			);
	}

	calcularMultaJuros(empresa: number, parcelas: any): Observable<any> {
		return this.http
			.post(
				this.admRest.buildFullUrlZwBack("caixa-aberto/calcular-multa-juros"),
				{
					empresa: empresa,
					parcelas: parcelas,
				}
			)
			.pipe(
				map((response: any) => {
					return response.content;
				})
			);
	}

	validarParcelas(empresa: number, parcelas: any[]): Observable<any> {
		return this.http
			.post(this.admRest.buildFullUrlZwBack("caixa-aberto/validar-parcelas"), {
				empresa: empresa,
				parcelas: parcelas,
			})
			.pipe(
				map((response: any) => {
					return response.content;
				})
			);
	}
	receberPagamentoComTagFull(
		body: BodyPagamento,
		pagamentosEspecial,
		itemType
	) {
		return this.http
			.post(this.admRest.buildFullUrlZwBack(`recebimento/${itemType}`), {
				...body,
				...pagamentosEspecial,
			})
			.pipe(
				map((response: any) => {
					return response.content;
				})
			);
	}

	receberPagamento(body: BodyPagamento, pagamentos: Array<Object>): any {
		return this.http
			.post(this.admRest.buildFullUrlZwBack("recebimento/pagar"), {
				...body,
				pagamentos: pagamentos,
			})
			.pipe(
				map((response: any) => {
					return response.content;
				})
			);
	}

	consultarSituacaoPix(codigoPix: number) {
		return this.http
			.get(this.admRest.buildFullUrlZwBack("recebimento/pix/" + codigoPix), {
				headers: {
					empresaId: this.empresaSelecionada.toString(),
				},
			})
			.pipe(
				map((response: any) => {
					return response.content;
				})
			);
	}

	emailPix(pix: number, email: string): any {
		return this.http
			.post(this.admRest.buildFullUrlZwBack("recebimento/pix-email"), {
				pix,
				email,
			})
			.pipe(
				map((response: any) => {
					return response.content;
				})
			);
	}

	cancelamento(
		usuario: number,
		justificativa: string,
		parcelas: number[]
	): any {
		return this.http
			.post(this.admRest.buildFullUrlZwBack("caixa-aberto/cancelar-parcelas"), {
				usuario: usuario,
				justificativa: justificativa,
				parcelas: parcelas,
			})
			.pipe(
				map((response: any) => {
					return response.content;
				})
			);
	}

	cancelarBoletosPendentes(
		usuario: number,
		justificativa: string,
		boletos: number[]
	): any {
		return this.http
			.post(this.admRest.buildFullUrlZwBack("caixa-aberto/cancelar-boletos"), {
				usuario: usuario,
				justificativa: justificativa,
				boletos: boletos,
			})
			.pipe(
				map((response: any) => {
					return response.content;
				})
			);
	}

	formasPagamento() {
		return this.http
			.get(this.admRest.buildFullUrlZwBack("recebimento/forma-pagamento"), {
				headers: {
					empresaId: this.empresaSelecionada.toString(),
				},
			})
			.pipe(
				map((response: any) => {
					return response.content;
				})
			);
	}

	getAdquirente() {
		return this.http
			.get(this.admRest.buildFullUrlZwBack(`recebimento/adquirente`))
			.pipe(
				map((response: any) => {
					return response.content;
				})
			);
	}
	getOperadoraCartao(codTipoPagamento) {
		return this.http
			.get(
				this.admRest.buildFullUrlZwBack(
					`recebimento/operadora-cartao/${codTipoPagamento}`
				)
			)
			.pipe(
				map((response: any) => {
					return response.content;
				})
			);
	}
	getBanco() {
		return this.http
			.get(this.admRest.buildFullUrlZwBack(`recebimento/banco`))
			.pipe(
				map((response: any) => {
					return response.content;
				})
			);
	}
	getAutorizacao(codUsuarioResponsavel) {
		return this.http
			.get(
				this.admRest.buildFullUrlZwBack(
					`recebimento/autorizacao/${codUsuarioResponsavel}`
				)
			)
			.pipe(
				map((response: any) => {
					return response.content;
				})
			);
	}
	getContaCorrente(pessoa) {
		return this.http
			.get(
				this.admRest.buildFullUrlZwBack(`recebimento/conta-corrente/${pessoa}`)
			)
			.pipe(
				map((response: any) => {
					return response.content;
				})
			);
	}

	getConvenios(
		tipoPagamento:
			| "PIX"
			| "ONLINE"
			| "BOLETO_GERAL"
			| "BOLETO"
			| "BOLETO_ONLINE",
		formaPagamento
	) {
		return this.http
			.get(
				this.admRest.buildFullUrlZwBack(
					`recebimento/convenio-cobranca/${tipoPagamento}/${formaPagamento}`
				),
				{
					headers: { empresaId: this.empresaSelecionada.toString() },
				}
			)
			.pipe(
				map((response: any) => {
					return response.content;
				})
			);
	}

	renegociarParcelas(
		usuario: number,
		justificativa,
		parcelasAnt: number[],
		parcelasNovas: []
	): Observable<ParcelasModel> {
		return this.http
			.post(
				this.admRest.buildFullUrlZwBack("caixa-aberto/renegociar-parcelas"),
				{
					usuario: usuario,
					justificativa: justificativa,
					parcelas_anteriores: parcelasAnt,
					parcelas_novas: parcelasNovas,
				}
			)
			.pipe(
				map((response: any) => {
					return response;
				})
			);
	}

	boleto(
		simular: boolean,
		empresa: number,
		usuario: number,
		pessoa: number,
		cobrarMultaJuros: boolean,
		valor: number,
		parcelas: any,
		boleto: any
	): Observable<any> {
		return this.http
			.post(
				this.admRest.buildFullUrlZwBack(
					simular ? "recebimento/boleto/simular" : "recebimento/boleto"
				),
				{
					usuario,
					pessoa,
					empresa,
					valor,
					cobrarMultaJuros,
					parcelas,
					boleto,
				}
			)
			.pipe(
				map((response: any) => {
					return response.content;
				})
			);
	}

	convenioCobrancaBoletoGeral() {
		return this.http
			.get(
				this.admRest.buildFullUrlZwBack(
					"recebimento/convenio-cobranca/BOLETO_GERAL/0"
				),
				{}
			)
			.pipe(
				map((response: any) => {
					return response.content;
				})
			);
	}

	infoGerarBoleto(empresa) {
		return this.http
			.get(this.admRest.buildFullUrlZwBack("recebimento/boleto/info"), {
				headers: {
					empresaId: empresa.toString(),
				},
			})
			.pipe(
				map((response: any) => {
					return response.content;
				})
			);
	}
}

export class BodyPagamento {
	pessoa: Number;
	tipoComprador: string;
	nomeComprador: string;
	dataPagamento: Number;
	usuario: Number;
	observacao: string;
	empresa: Number;
	parcelas: Array<Object>;
	valor: Number;
	valorParcelas: Number;
	valorSobressalenteResidual: Number;
	valorContaCorrente: Number;
	valorDebitoContaCorrente: Number;
	valorCreditoContaCorrente: Number;
	valorMultaJuros: Number;
	cobrarMultaJuros: boolean;
}
