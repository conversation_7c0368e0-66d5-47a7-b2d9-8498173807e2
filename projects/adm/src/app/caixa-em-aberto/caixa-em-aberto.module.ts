import { CommonModule } from "@angular/common";
import { CUSTOM_ELEMENTS_SCHEMA, NgModule } from "@angular/core";
import { FormsModule, ReactiveFormsModule } from "@angular/forms";
import { RouterModule } from "@angular/router";
import { NgxCurrencyModule } from "ngx-currency";
import { NgxMaskModule } from "ngx-mask";
import { OldUiKitModule } from "old-ui-kit";
import { SdkModule } from "sdk";
import { UiModule } from "ui-kit";
import { LayoutModule } from "../layout/layout.module";
import { routes } from "./caixa-em-aberto-routing.module";
import { CaixaEmAbertoComponent } from "./caixa-em-aberto.component";
import { CaixaEmAbertoService } from "./caixa-em-aberto.service";
import { FinalizarPagamentoComponent } from "./finalizar-pagamento/finalizar-pagamento.component";
import { GerarBoletoComponent } from "./gerar-boleto/gerar-boleto.component";
import { BoletoComponent } from "./receber-parcelas/boleto/boleto.component";
import { CartaoDeCreditoOfflineComponent } from "./receber-parcelas/cartao-de-credito-offline/cartao-de-credito-offline.component";
import { CartaoDeCreditoOnlineComponent } from "./receber-parcelas/cartao-de-credito-online/cartao-de-credito-online.component";
import { CartaoDeDebitoComponent } from "./receber-parcelas/cartao-de-debito/cartao-de-debito.component";
import { ChequeComponent } from "./receber-parcelas/cheque/cheque.component";
import { ContaCorrenteComponent } from "./receber-parcelas/conta-corrente/conta-corrente.component";
import { DinheiroComponent } from "./receber-parcelas/dinheiro/dinheiro.component";
import {
	CartaoPipe,
	PagamentoOnlineComponent,
} from "./receber-parcelas/pagamento-online/pagamento-online.component";
import { PixComponent } from "./receber-parcelas/pix/pix.component";
import { ReceberParcelasComponent } from "./receber-parcelas/receber-parcelas.component";
import { TafComponent } from "./receber-parcelas/taf/taf.component";
import { TransferenciaBancariaComponent } from "./receber-parcelas/transferencia-bancaria/transferencia-bancaria.component";
import { ModalRenegociacaoJustificativaComponent } from "./renegociar-parcelas/modal-renegociacao-justificativa/modal-renegociacao-justificativa.component";
import { RenegociarParcelasComponent } from "./renegociar-parcelas/renegociar-parcelas.component";
import { TableRenegociacaoComponent } from "./renegociar-parcelas/table-renegociacao/table-renegociacao.component";
import { CompartilharBoletoComponent } from "./gerar-boleto/compartilhar-boleto/compartilhar-boleto.component";
import { CancelarParcelasComponent } from "./cancelar-parcelas/cancelar-parcelas.component";
import { ModalBoletoPendenteComponent } from "./modal-boleto-pendente/modal-boleto-pendente.component";
import { BaseSharedModule } from "@base-shared/base-shared.module";
import { ModalConfirmarRecebimentoComponent } from "./receber-parcelas/modal-confirmar-recebimento/modal-confirmar-recebimento.component";

@NgModule({
	declarations: [
		CaixaEmAbertoComponent,
		ReceberParcelasComponent,
		RenegociarParcelasComponent,
		BoletoComponent,
		CartaoDeCreditoOfflineComponent,
		CartaoDeCreditoOnlineComponent,
		CartaoDeDebitoComponent,
		ChequeComponent,
		ContaCorrenteComponent,
		DinheiroComponent,
		PixComponent,
		TransferenciaBancariaComponent,
		PagamentoOnlineComponent,
		TafComponent,
		FinalizarPagamentoComponent,
		TableRenegociacaoComponent,
		ModalRenegociacaoJustificativaComponent,
		CartaoPipe,
		GerarBoletoComponent,
		CompartilharBoletoComponent,
		CancelarParcelasComponent,
		ModalBoletoPendenteComponent,
		ModalConfirmarRecebimentoComponent,
	],
	imports: [
		CommonModule,
		RouterModule.forChild(routes),
		SdkModule,
		ReactiveFormsModule,
		FormsModule,
		LayoutModule,
		UiModule,
		NgxCurrencyModule,
		NgxMaskModule,
		OldUiKitModule,
		BaseSharedModule,
	],
	entryComponents: [
		ModalRenegociacaoJustificativaComponent,
		CompartilharBoletoComponent,
		CancelarParcelasComponent,
		ModalBoletoPendenteComponent,
		ModalConfirmarRecebimentoComponent,
	],
	schemas: [CUSTOM_ELEMENTS_SCHEMA],
})
export class CaixaEmAbertoModule {}
