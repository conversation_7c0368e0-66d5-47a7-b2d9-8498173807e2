import { ChangeDetectorRef, Component, OnInit, AfterViewInit } from "@angular/core";
import { FormBuilder, FormGroup, Validators } from "@angular/forms";
import { ActivatedRoute, Router } from "@angular/router";
import { SnotifyService } from "ng-snotify";
import {
	CadastroAuxApiModeloOrcamentoService,
	ModeloOrcamento,
	SITUACAO_MODELO_ORCAMENTO_OPTIONS,
} from "cadastro-aux-api";
import { AdmCoreApiModalidadeService } from "adm-core-api";
import { PlanoApiPacoteService } from "plano-api";
import Quill from "quill";
import BlotFormatter from "quill-blot-formatter/dist/BlotFormatter";
import {
	ClientDiscoveryService, PerfilAcessoRecurso,
	SessionService,
} from "sdk";
import { AdmRestService } from "@adm/adm-rest.service";
import { PerfilAcessoRecursoNome } from "@adm/perfil-acesso/perfil-acesso-recurso.model";

Quill.register("modules/blotFormatter", BlotFormatter);
Quill.import("attributors/style/size");

@Component({
	selector: "adm-modelo-orcamento-form",
	templateUrl: "./modelo-orcamento-form.component.html",
	styleUrls: ["./modelo-orcamento-form.component.scss"],
})
export class ModeloOrcamentoFormComponent implements OnInit, AfterViewInit {
	form: FormGroup;
	modeloOrcamento: ModeloOrcamento = new ModeloOrcamento();
	id: string;
	situacoes = SITUACAO_MODELO_ORCAMENTO_OPTIONS;
	modalidades = [];
	pacotes = [];
	isEdicao = false;
	urlLog: string;
	modules = {};
	fontSizeArr = [
		"8px",
		"9px",
		"10px",
		"11px",
		"12px",
		"14px",
		"16px",
		"18px",
		"20px",
		"24px",
		"32px",
		"36px",
		"42px",
		"54px",
		"68px",
		"84px",
		"98px",
	];
	Size = Quill.import("attributors/style/size");
	recurso: PerfilAcessoRecurso;

	constructor(
		private fb: FormBuilder,
		private router: Router,
		private admRest: AdmRestService,
		private activatedRoute: ActivatedRoute,
		private sessionService: SessionService,
		private notificationService: SnotifyService,
		private modeloOrcamentoService: CadastroAuxApiModeloOrcamentoService,
		private readonly clientDiscoveryService: ClientDiscoveryService,
		private modalidadeService: AdmCoreApiModalidadeService,
		private pacoteService: PlanoApiPacoteService,
		private cd: ChangeDetectorRef,
	) {
		this.recurso = this.sessionService.recursos.get(
			PerfilAcessoRecursoNome.MODELO_ORCAMENTO,
		);
	}

	ngOnInit(): void {
		this.id = this.activatedRoute.snapshot.paramMap.get("id");
		this.isEdicao = !!this.id && this.id !== "novo";

		this.form = this.fb.group({
			codigo: [{ value: "", disabled: true }],
			descricao: ["", [Validators.required]],
			situacao: ["", [Validators.required]],
			modalidade: [null, [Validators.required]],
			pacote: [null],
			dataDefinicao: [new Date(), [Validators.required]],
			responsavelDefinicao: [null, [Validators.required]],
			texto: ["", [Validators.required]],
		});

		this.modules = {
			blotFormatter: {},
			toolbar: {
				container: [
					["bold", "italic", "underline"],
					[{ list: "ordered" }, { list: "bullet" }],
					[{ script: "sub" }, { script: "super" }],
					[{ indent: "-1" }, { indent: "+1" }],
					[{ size: this.fontSizeArr }],
					[{ header: [1, 2, 3, 4, 5, 6, false] }],
					[{ color: [] }, { background: [] }],
					[{ align: [] }],
					["clean"],
					["link", "image"],
				],
			},
		};

		if (
			this.isEdicao &&
			(this.recurso.consultar || this.recurso.incluirConsultar) &&
			!this.recurso.editar &&
			!this.recurso.incluir
		) {
			this.form.disable();
		}

		this.carregarModalidades();
		this.carregarPacotes();

		if (this.isEdicao) {
			this.carregarModeloOrcamento();
			this.urlLog = this.admRest.buildFullUrlCadAux(
				`log/MODELOORCAMENTO/${this.id}`,
			);
		} else {
			this.form.get("situacao").setValue("AT");
			this.form.get("dataDefinicao").setValue(new Date());
			this.form
				.get("responsavelDefinicao")
				.setValue(this.sessionService.codUsuarioZW);
		}
	}

	ngAfterViewInit() {
		this.scrollToComponent('adm-layout-modelo-orcamento');
	}

	scrollToComponent(idComponent) {
		const el = document.getElementById(idComponent);
		if (el) {
			el.scrollIntoView();
		}
	}

	carregarModalidades() {
		this.modalidadeService
			.list({ page: 0, size: 500, filtrarEmpresa: false, utilizarTurma: "A" })
			.subscribe(
				(response) => {
					this.modalidades = response.content || [];
					this.cd.detectChanges();
				},
				(error) => {
					this.notificationService.error("Erro ao carregar modalidades");
				},
			);
	}

	carregarPacotes() {
		this.pacotes = [];
		this.pacoteService.list({ page: 0, size: 500 }).subscribe(
			(response) => {
				this.pacotes = response.content || [];
				this.cd.detectChanges();
			},
			(error) => {
				this.notificationService.error("Erro ao carregar pacotes");
			},
		);
	}

	carregarModeloOrcamento() {
		this.modeloOrcamentoService.find(this.id).subscribe(
			(response) => {
				response = response.content;
				if (
					response.texto &&
					response.texto !== undefined &&
					response.texto.includes("Untitled document")
				) {
					response.texto = response.texto.replace("Untitled document", "");
				}
				this.modeloOrcamento = response;
				this.ajustarHtmlGeradoPelaTelaAntiga();
				this.atualizarEstiloTextoParaQuill();

				this.form.patchValue(this.modeloOrcamento);
				if (response.dataDefinicao) {
					this.form
						.get("dataDefinicao")
						.setValue(new Date(response.dataDefinicao));
				}
				if (response.modalidade) {
					this.form.get("modalidade").setValue(response.modalidade.codigo);
				}
				if (response.pacote) {
					this.form.get("pacote").setValue(response.pacote.codigo);
				}
				if (response.responsavelDefinicao) {
					this.form
						.get("responsavelDefinicao")
						.setValue(response.responsavelDefinicao.codigo);
				}
				this.cd.detectChanges();
			},
			(error) => {
				this.notificationService.error("Erro ao carregar modelo de orçamento");
				this.voltarListagem();
			},
		);
	}

	private ajustarHtmlGeradoPelaTelaAntiga() {
		if (
			this.modeloOrcamento.texto &&
			this.modeloOrcamento.texto !== undefined
		) {
			// Substituir as nomeclaturas do font-size que eram utilizadas na tela antiga
			this.modeloOrcamento.texto = this.modeloOrcamento.texto.replace(
				new RegExp("font-size: xx-small", "g"),
				"font-size: 8px",
			);
			this.modeloOrcamento.texto = this.modeloOrcamento.texto.replace(
				new RegExp("font-size: x-small", "g"),
				"font-size: 10px",
			);
			this.modeloOrcamento.texto = this.modeloOrcamento.texto.replace(
				new RegExp("font-size: small", "g"),
				"font-size: 12px",
			);
			this.modeloOrcamento.texto = this.modeloOrcamento.texto.replace(
				new RegExp("font-size: medium", "g"),
				"14px",
			);
			this.modeloOrcamento.texto = this.modeloOrcamento.texto.replace(
				new RegExp("font-size: large", "g"),
				"font-size: 18px",
			);
			this.modeloOrcamento.texto = this.modeloOrcamento.texto.replace(
				new RegExp("font-size: x-large", "g"),
				"font-size: 24px",
			);
			this.modeloOrcamento.texto = this.modeloOrcamento.texto.replace(
				new RegExp("font-size: xx-large", "g"),
				"font-size: 36px",
			);

			// Substituir font-size:10.0pt por font-size: 10px;
			for (let i = 8; i <= 36; i++) {
				for (let j = 0; j < 10; j++) {
					this.modeloOrcamento.texto = this.modeloOrcamento.texto.replace(
						new RegExp(`font-size:${i}.${j}pt;`, "g"),
						`font-size: ${i}.${j}px;`,
					);
					this.modeloOrcamento.texto = this.modeloOrcamento.texto.replace(
						new RegExp(`font-size: ${i}.${j}pt;`, "g"),
						`font-size: ${i}.${j}px;`,
					);
				}
			}

			// Adicionar um font size padrão nas tags que não tem font size
			this.adicionarFontSize("span", "11px");
			this.adicionarFontSize("strong", "11px");

			this.modeloOrcamento.texto = this.modeloOrcamento.texto.replace(
				new RegExp("<p", "g"),
				"<div><br></div> <div",
			);
			this.modeloOrcamento.texto = this.modeloOrcamento.texto.replace(
				new RegExp("</p>", "g"),
				"</div>",
			);
		}
	}

	atualizarEstiloTextoParaQuill() {
		if (
			this.modeloOrcamento.texto &&
			this.modeloOrcamento.texto !== undefined
		) {
			if (this.modeloOrcamento.texto.includes("style=\"padding-left: 30px;\"")) {
				this.modeloOrcamento.texto = this.modeloOrcamento.texto
					.split("style=\"padding-left: 30px;\"")
					.join("class=\"ql-indent-1\"");
			}
			if (this.modeloOrcamento.texto.includes("style=\"padding-left: 60px;\"")) {
				this.modeloOrcamento.texto = this.modeloOrcamento.texto
					.split("style=\"padding-left: 60px;\"")
					.join("class=\"ql-indent-2\"");
			}
			if (this.modeloOrcamento.texto.includes("style=\"padding-left: 90px;\"")) {
				this.modeloOrcamento.texto = this.modeloOrcamento.texto
					.split("style=\"padding-left: 90px;\"")
					.join("class=\"ql-indent-3\"");
			}
			if (this.modeloOrcamento.texto.includes("style=\"padding-left: 120px;\"")) {
				this.modeloOrcamento.texto = this.modeloOrcamento.texto
					.split("style=\"padding-left: 120px;\"")
					.join("class=\"ql-indent-4\"");
			}
			if (this.modeloOrcamento.texto.includes("style=\"padding-left: 150px;\"")) {
				this.modeloOrcamento.texto = this.modeloOrcamento.texto
					.split("style=\"padding-left: 150px;\"")
					.join("class=\"ql-indent-5\"");
			}
			if (this.modeloOrcamento.texto.includes("style=\"padding-left: 180px;\"")) {
				this.modeloOrcamento.texto = this.modeloOrcamento.texto
					.split("style=\"padding-left: 180px;\"")
					.join("class=\"ql-indent-6\"");
			}
		}
	}

	private adicionarFontSize(tagName: string, value: string) {
		const tagsHtml = new Array<any>();
		let count = 0;
		this.modeloOrcamento.texto.split(`<${tagName}`).forEach((t) => {
			if (count > 0) {
				const index = t.indexOf(">");
				if (index >= 0) {
					let tagHtml = `<${tagName}`;
					tagHtml += t.substring(0, index + 1);
					if (tagHtml.includes("style") && !tagHtml.includes("font-size:")) {
						const indexStyle = tagHtml.indexOf("style=");
						const tagAjustada =
							tagHtml.substring(0, indexStyle + 7) +
							`font-size: ${value};` +
							tagHtml.substring(indexStyle + 7);
						tagsHtml.push({ tag: tagHtml, tagAjustada });
					} else if (!tagHtml.includes("font-size")) {
						const tagAjustada =
							tagHtml.substring(0, tagName.length + 1) +
							` style="font-size: ${value};" ` +
							tagHtml.substring(tagName.length + 1);
						tagsHtml.push({ tag: tagHtml, tagAjustada });
					}
				}
			}
			count++;
		});

		tagsHtml.forEach((v) => {
			if (v.tag !== undefined && v.tagAjustada !== undefined) {
				this.modeloOrcamento.texto = this.modeloOrcamento.texto.replace(
					new RegExp(v.tag, "g"),
					v.tagAjustada,
				);
			}
		});
	}

	voltarListagem() {
		this.router.navigate(["adm", "config-financeiras", "modelo-orcamento"]);
	}

	salvar(): void {
		if (
			!this.recurso ||
			!(
				this.recurso.editar ||
				this.recurso.incluir ||
				!(
					this.isEdicao &&
					(this.recurso.consultar || this.recurso.incluirConsultar)
				)
			)
		) {
			let resource = "INCLUIR";
			if (this.isEdicao) {
				resource = "EDITAR";
			}
			this.notificationService.error(
				`Você não possui permissão para esta operação, "${resource} 5.18 - ORÇAMENTO TURMAS"`,
				{
					timeout: 5000,
					bodyMaxLength: 300,
				}
			);
			return;
		}

		if (this.form.invalid) {
			this.notificationService.error("Preencha todos os campos obrigatórios.");
			return;
		}

		const dadosFormulario = this.form.getRawValue();
		Object.assign(this.modeloOrcamento, dadosFormulario);

		if (this.form.get("modalidade").value) {
			this.modeloOrcamento.modalidade = {
				codigo: this.form.get("modalidade").value,
			};
		}
		if (this.form.get("pacote").value) {
			this.modeloOrcamento.pacote = {
				codigo: this.form.get("pacote").value,
			};
		}
		if (this.form.get("responsavelDefinicao").value) {
			this.modeloOrcamento.responsavelDefinicao = {
				codigo: this.form.get("responsavelDefinicao").value,
			};
		}

		this.validarTexto();
		this.prepararHtmlTexto();

		this.modeloOrcamentoService.save(this.modeloOrcamento).subscribe(
			(response) => {
				this.notificationService.success(
					this.isEdicao
						? "Modelo de Orçamento atualizado com sucesso!"
						: "Modelo de Orçamento cadastrado com sucesso!",
				);
				this.voltarListagem();
			},
			(error) => {
				const errorMessage =
					error && error.error && error.error.meta && error.error.meta.message
						? error.error.meta.message
						: "Erro ao salvar modelo de orçamento.";
				this.notificationService.error(errorMessage);
			},
		);
	}

	prepararHtmlTexto() {
		if (
			!this.modeloOrcamento.texto.includes(
				"<!DOCTYPE html PUBLIC \"-//W3C//DTD XHTML 1.0 Transitional//EN\" \"http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd\">",
			)
		) {
			const padraoTexto =
				"<!DOCTYPE html PUBLIC \"-//W3C//DTD XHTML 1.0 Transitional//EN\" \"http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd\">\n" +
				"<html>\n" +
				"<head>\n" +
				"<meta charset=\"UTF-8\"/>\n" +
				"<title></title>\n" +
				"</head>\n" +
				"<body>\n" +
				this.modeloOrcamento.texto.replace(/(\r\n|\n|\r)/gm, "") +
				"\n" +
				"</body>\n" +
				"</html>";

			this.modeloOrcamento.texto = padraoTexto;
		}
	}

	validarTexto() {
		// validações de alinhamento
		if (this.modeloOrcamento.texto.includes("class=\"ql-align-center\"")) {
			this.modeloOrcamento.texto = this.modeloOrcamento.texto
				.split("class=\"ql-align-center\"")
				.join("style=\"text-align: center;\"");
		}
		if (this.modeloOrcamento.texto.includes("class=\"ql-align-right\"")) {
			this.modeloOrcamento.texto = this.modeloOrcamento.texto
				.split("class=\"ql-align-right\"")
				.join("style=\"text-align: right;\"");
		}
		if (this.modeloOrcamento.texto.includes("class=\"ql-align-justify\"")) {
			this.modeloOrcamento.texto = this.modeloOrcamento.texto
				.split("class=\"ql-align-justify\"")
				.join("style=\"text-align: justify;\"");
		}

		// validações de identação
		if (this.modeloOrcamento.texto.includes("class=\"ql-indent-1\"")) {
			this.modeloOrcamento.texto = this.modeloOrcamento.texto
				.split("class=\"ql-indent-1\"")
				.join("style=\"padding-left: 30px;\"");
		}
		if (this.modeloOrcamento.texto.includes("class=\"ql-indent-2\"")) {
			this.modeloOrcamento.texto = this.modeloOrcamento.texto
				.split("class=\"ql-indent-2\"")
				.join("style=\"padding-left: 60px;\"");
		}
		if (this.modeloOrcamento.texto.includes("class=\"ql-indent-3\"")) {
			this.modeloOrcamento.texto = this.modeloOrcamento.texto
				.split("class=\"ql-indent-3\"")
				.join("style=\"padding-left: 90px;\"");
		}
		if (this.modeloOrcamento.texto.includes("class=\"ql-indent-4\"")) {
			this.modeloOrcamento.texto = this.modeloOrcamento.texto
				.split("class=\"ql-indent-4\"")
				.join("style=\"padding-left: 120px;\"");
		}
		if (this.modeloOrcamento.texto.includes("class=\"ql-indent-5\"")) {
			this.modeloOrcamento.texto = this.modeloOrcamento.texto
				.split("class=\"ql-indent-5\"")
				.join("style=\"padding-left: 150px;\"");
		}
		if (this.modeloOrcamento.texto.includes("class=\"ql-indent-6\"")) {
			this.modeloOrcamento.texto = this.modeloOrcamento.texto
				.split("class=\"ql-indent-6\"")
				.join("style=\"padding-left: 180px;\"");
		}
	}

	novo() {
		this.form.reset();
		this.modeloOrcamento = new ModeloOrcamento();
		this.isEdicao = false;
		this.cd.detectChanges();
	}

	imprimirModeloOrcamento() {
		const textoFormulario = this.form.get("texto");
		const texto = textoFormulario ? textoFormulario.value : "";
		const popupWindow = window.open(
			"",
			"_blank",
			"top=0,left=0,height=100%,width=auto",
		);
		popupWindow.document.open();
		popupWindow.document.write(`
            <html>
                <head>
                    <title>Modelo de Orçamento</title>
                    <style></style>
                </head>
                <body onload="window.print();window.close()">
                    ${texto}
                </body>
            </html>`);
		popupWindow.document.close();
	}

	imprimir() {
		if (!this.modeloOrcamento.codigo) {
			this.notificationService.info("Salve o modelo antes de imprimir.");
			return;
		}
		const trueUrl = `${
			this.clientDiscoveryService.getUrlMap().zwUrl
		}/faces/VisualizarContrato?k=${this.sessionService.chave}&mo=${
			this.modeloOrcamento.codigo
		}&e=${this.sessionService.empresaId}`;
		window.open(trueUrl, "_blank");
	}

	consultar() {
		this.notificationService.info(
			"Funcionalidade de consulta será implementada.",
		);
	}

	cancelar() {
		this.voltarListagem();
	}

	get tituloFormulario(): string {
		return this.isEdicao
			? "Editar Modelo de Orçamento"
			: "Novo Modelo de Orçamento";
	}
}
