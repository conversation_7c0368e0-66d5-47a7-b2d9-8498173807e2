import {
	AfterViewInit,
	ChangeDetectorRef,
	Component,
	OnInit,
	TemplateRef,
	ViewChild,
} from "@angular/core";
import { Router } from "@angular/router";
import { NgbModal } from "@ng-bootstrap/ng-bootstrap";
import { CadastroAuxApiModeloOrcamentoService } from "cadastro-aux-api";
import { SnotifyService } from "ng-snotify";
import { Api, PerfilAcessoRecurso, SessionService } from "sdk";
import {
	ConfirmDialogDeleteComponent,
	GridFilterConfig,
	GridFilterType,
	PactoDataGridConfig,
	RelatorioComponent,
	TraducoesXinglingComponent,
} from "ui-kit";
import { AdmRestService } from "../../../../adm-rest.service";
import { PerfilAcessoRecursoNome } from "@adm/perfil-acesso/perfil-acesso-recurso.model";

declare var moment;

@Component({
	selector: "adm-modelo-orcamento",
	templateUrl: "./modelo-orcamento.component.html",
	styleUrls: ["./modelo-orcamento.component.scss"],
})
export class ModeloOrcamentoComponent implements OnInit, AfterViewInit {
	@ViewChild("traducao", { static: true }) traducao: TraducoesXinglingComponent;
	@ViewChild("columnCodigo", { static: true }) columnCodigo: TemplateRef<any>;
	@ViewChild("columnDescricao", { static: true })
	columnDescricao: TemplateRef<any>;
	@ViewChild("columnSituacao", { static: true })
	columnSituacao: TemplateRef<any>;
	@ViewChild("columnModalidade", { static: true })
	columnModalidade: TemplateRef<any>;
	@ViewChild("columnPacote", { static: true })
	columnPacote: TemplateRef<any>;
	@ViewChild("columnDataDefinicao", { static: true })
	columnDataDefinicao: TemplateRef<any>;
	@ViewChild("columnResponsavel", { static: true })
	columnResponsavel: TemplateRef<any>;
	@ViewChild("tableModeloOrcamento", { static: false })
	tableModeloOrcamento: RelatorioComponent;
	table: PactoDataGridConfig;
	filterConfig: GridFilterConfig = { filters: [] };
	recurso: PerfilAcessoRecurso;

	constructor(
		private router: Router,
		private cd: ChangeDetectorRef,
		private sessionService: SessionService,
		private admRest: AdmRestService,
		private notificationService: SnotifyService,
		private modeloOrcamentoService: CadastroAuxApiModeloOrcamentoService,
		private ngbModal: NgbModal,
	) {
		this.recurso = this.sessionService.recursos.get(
			PerfilAcessoRecursoNome.MODELO_ORCAMENTO,
		);
	}

	voltarHome() {
		this.router.navigate(["adm"]);
	}

	novoModeloOrcamento() {
		if (!this.recurso || (!this.recurso.incluir && !this.recurso.incluirConsultar)) {
			this.notificationService.error(
				"Você não possui permissão para esta operação, \"INCLUIR 5.18 - ORÇAMENTO TURMAS\"",
				{
					timeout: 5000,
					bodyMaxLength: 300,
				},
			);
			return;
		}

		this.router.navigate([
			"adm",
			"config-financeiras",
			"modelo-orcamento",
			"novo",
		]);
	}

	iconClickFn(event: { row: any; iconName: string }) {
		if (event.iconName === "editModeloOrcamento") {
			this.editModeloOrcamento(event.row);
		} else if (event.iconName === "deleteModeloOrcamento") {
			this.openModalConfirmacaoExclusao(event);
		}
	}

	openModalConfirmacaoExclusao(event) {
		if (!this.recurso || (!this.recurso.excluir)) {
			this.notificationService.error(
				"Você não possui permissão para esta operação, \"EXCLUIR 5.18 - ORÇAMENTO TURMAS\"",
				{
					timeout: 5000,
					bodyMaxLength: 300,
				},
			);
			return;
		}

		const modalConfirmacao = this.ngbModal.open(ConfirmDialogDeleteComponent, {
			windowClass: "modal-confirmacao",
		});

		modalConfirmacao.componentInstance.message =
			"Deseja realmente excluir este modelo de orçamento?";

		modalConfirmacao.result
			.then((excluir) => {
				if (excluir) {
					this.deleteModeloOrcamento(event.row);
				}
			})
			.catch((error) => {
			});

		this.cd.detectChanges();
	}

	ngOnInit() {
		this.initTable();
		this.cd.detectChanges();
	}

	ngAfterViewInit() {
		this.initFilter();
	}

	editModeloOrcamento(modeloOrcamento) {
		if (!this.recurso || (!this.recurso.editar && !this.recurso.incluirConsultar && !this.recurso.consultar)) {
			this.notificationService.error(
				"Você não possui permissão para esta operação, \"CONSULTAR 5.18 - ORÇAMENTO TURMAS\"",
				{
					timeout: 5000,
					bodyMaxLength: 300,
				},
			);
			return;
		}
		this.router.navigate([
			"adm",
			"config-financeiras",
			"modelo-orcamento",
			modeloOrcamento.codigo,
		]);
	}

	deleteModeloOrcamento(row: any) {
		this.modeloOrcamentoService.delete(row.codigo).subscribe(
			(response) => {
				this.notificationService.success(this.traducao.getLabel("DELETED"));
				if (this.tableModeloOrcamento.data.content) {
					this.tableModeloOrcamento.data.content =
						this.tableModeloOrcamento.data.content.filter(
							(obj) => obj.codigo !== row.codigo,
						);
				}
				this.tableModeloOrcamento.reloadData();
			},
			(httpErrorResponse) => {
				console.log(httpErrorResponse);
				const err = httpErrorResponse.error;
				if (err.meta && err.meta.messageValue) {
					this.notificationService.error(err.meta.messageValue);
				} else {
					this.notificationService.error(err);
				}
			},
		);
	}

	private initFilter() {
		setTimeout(() => {
			this.filterConfig = {
				filters: [
					{
						name: "situacao",
						label: this.traducao.getLabel("filtro-situacao"),
						type: GridFilterType.DS3_CHIPS,
						options: [
							{ value: "AT", label: this.traducao.getLabel("ativo") },
							{ value: "IN", label: this.traducao.getLabel("inativo") },
						],
					},
				],
			};
			this.cd.detectChanges();
		});
	}

	private initTable() {
		setTimeout(() => {
			this.table = new PactoDataGridConfig({
				endpointUrl: this.admRest.buildFullUrl(
					"/modelo-orcamento",
					false,
					Api.MSCADAUX,
				),
				logUrl: this.admRest.buildFullUrlCadAux("log/MODELOORCAMENTO"),
				quickSearch: true,
				showFilters: true,
				columns: [
					{
						nome: "codigo",
						titulo: this.columnCodigo,
						visible: true,
						ordenavel: true,
					},
					{
						nome: "descricao",
						titulo: this.columnDescricao,
						visible: true,
						ordenavel: true,
					},
					{
						nome: "situacao",
						titulo: this.columnSituacao,
						visible: true,
						ordenavel: true,
						valueTransform: (v) =>
							v === "AT"
								? this.traducao.getLabel("ativo")
								: this.traducao.getLabel("inativo"),
					},
					{
						nome: "modalidade",
						titulo: this.columnModalidade,
						visible: true,
						ordenavel: true,
						valueTransform: (v) => (v ? v.nome : "-"),
					},
					{
						nome: "pacote",
						titulo: this.columnPacote,
						visible: true,
						ordenavel: true,
						valueTransform: (v) => (v ? v.descricao : "-"),
					},
					{
						nome: "dataDefinicao",
						titulo: this.columnDataDefinicao,
						visible: true,
						ordenavel: true,
						valueTransform: (v) => moment(v).format("DD/MM/YYYY"),
						date: true,
					},
					{
						nome: "responsavelDefinicao",
						titulo: this.columnResponsavel,
						visible: true,
						ordenavel: true,
						valueTransform: (v) => (v ? v.nome : "-"),
					},
				],
				actions: [
					{
						nome: "editModeloOrcamento",
						iconClass: "pct pct-edit cor-action-default-able04",
						tooltipText: this.traducao.getLabel("TOOLTIP_EDIT"),
					},
					{
						nome: "deleteModeloOrcamento",
						iconClass: "pct pct-trash-2 cor-action-default-risk04",
						tooltipText: this.traducao.getLabel("TOOLTIP_DELETE"),
					},
				],
			});
			this.cd.detectChanges();
		});
	}
}
